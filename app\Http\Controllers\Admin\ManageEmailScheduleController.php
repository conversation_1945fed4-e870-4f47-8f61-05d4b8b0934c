<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Helpers\DataTableHelper;
use App\Models\v1\EmailScheduler;
use App\Models\v1\EmailTemplate;
use App\Models\v1\EmailCondition;
use App\V2\Core\Services\EmailSchedulerService;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ManageEmailScheduleController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $params = DataTableHelper::getParams($request);

            if ($params['columnName'] == 'id' || empty($params['columnName'])) {
                $params['columnName'] = 'email_schedules.id';
            }

            $qry = EmailScheduler::with('createdBy')
                ->orderBy($params['columnName'], $params['columnSortOrder'] ?? 'desc');

            $qry->where(function ($que) use ($params) {
                DataTableHelper::applySearchFilter($que, $params['searchValue'], $params['columns']);
            });

            [$count, $result] = DataTableHelper::applyPagination($qry, $params['row'], $params['rowperpage']);

            $data = [];
            foreach ($result as $schedule) {
                $data[] = [
                    'id' => $schedule->id,
                    'name' => $schedule->name,
                    'recurrence' => ucfirst($schedule->recurrence),
                    'recipients_to' => $this->formatRecipients($schedule->recipients_to),
                    'status' => $this->generateStatusBadge($schedule->status),
                    'action' => $this->generateActionButtons($schedule->id)
                ];
            }
            return DataTableHelper::generateResponse($params['draw'], $count, $data);
        }
        return view('admin.marketplace.email-schedules.index');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $events = config('email_scheduler.events', []);
        $recipientTypes = config('email_scheduler.recipient_types', []);
        $recurrenceOptions = config('email_scheduler.recurrence', []);
        $operators = config('email_scheduler.operators', []);
        $logicalOperators = config('email_scheduler.logical_operators', []);
        $models = config('email_scheduler.models', []);

        $templates = EmailTemplate::with('layout')->get();
        $conditions = EmailCondition::where('active', true)->get();

        return view('admin.marketplace.email-schedules.create', compact(
            'events', 'recipientTypes', 'recurrenceOptions', 'operators',
            'logicalOperators', 'templates', 'conditions', 'models'
        ));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'status' => 'required|in:enabled,disabled',
                'recurrence' => 'required|in:once,daily,weekly,monthly,yearly',
                'event_name' => 'nullable|string',
                'recipients_to' => 'nullable|array',
                'recipients_cc' => 'nullable|array',
                'template_ids' => 'nullable|array',
                'template_ids.*' => 'exists:email_templates,id',
                'condition_ids' => 'nullable|array',
                'condition_ids.*' => 'exists:email_conditions,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            // Create email schedule
            $schedule = EmailScheduler::create([
                'name' => $request->name,
                'status' => $request->status,
                'recurrence' => $request->recurrence,
                'event_name' => $request->event_name,
                'recipients_to' => $request->recipients_to ?? [],
                'recipients_cc' => $request->recipients_cc ?? [],
                'created_by' => auth()->id()
            ]);

            // Attach templates if provided
            if ($request->template_ids) {
                $schedule->templates()->attach($request->template_ids);
            }

            // Attach conditions if provided
            if ($request->condition_ids) {
                $schedule->conditions()->attach($request->condition_ids);
            }

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Email schedule created successfully',
                    'redirect' => url('admin/k12connections/manage-email-schedules')
                ]);
            }

            return redirect()->to('admin/k12connections/manage-email-schedules')
                           ->with('success', 'Email schedule created successfully');

        } catch (Exception $e) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to create email schedule: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()
                           ->withInput()
                           ->with('error', 'Failed to create email schedule: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        try {
            $schedule = EmailScheduler::with(['templates.layout', 'conditions', 'createdBy'])
                                    ->findOrFail($id);

            return view('admin.marketplace.email-schedules.show', compact('schedule'));
        } catch (Exception $e) {
            return redirect()->to('admin/k12connections/manage-email-schedules')
                           ->with('error', 'Email schedule not found');
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        try {
            $schedule = EmailScheduler::with(['templates', 'conditions'])->findOrFail($id);

            $events = config('email_scheduler.events', []);
            $recipientTypes = config('email_scheduler.recipient_types', []);
            $recurrenceOptions = config('email_scheduler.recurrence', []);
            $operators = config('email_scheduler.operators', []);
            $logicalOperators = config('email_scheduler.logical_operators', []);
            $models = config('email_scheduler.models', []);

            $templates = EmailTemplate::with('layout')->get();
            $conditions = EmailCondition::where('active', true)->get();

            return view('admin.marketplace.email-schedules.edit', compact(
                'schedule', 'events', 'recipientTypes', 'recurrenceOptions',
                'operators', 'logicalOperators', 'templates', 'conditions', 'models'
            ));
        } catch (Exception $e) {
            return redirect()->to('admin/k12connections/manage-email-schedules')
                           ->with('error', 'Email schedule not found');
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        try {
            $schedule = EmailScheduler::findOrFail($id);

            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'status' => 'required|in:enabled,disabled',
                'recurrence' => 'required|in:once,daily,weekly,monthly,yearly',
                'event_name' => 'nullable|string',
                'recipients_to' => 'nullable|array',
                'recipients_cc' => 'nullable|array',
                'template_ids' => 'nullable|array',
                'template_ids.*' => 'exists:email_templates,id',
                'condition_ids' => 'nullable|array',
                'condition_ids.*' => 'exists:email_conditions,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            // Update email schedule
            $schedule->update([
                'name' => $request->name,
                'status' => $request->status,
                'recurrence' => $request->recurrence,
                'event_name' => $request->event_name,
                'recipients_to' => $request->recipients_to ?? [],
                'recipients_cc' => $request->recipients_cc ?? []
            ]);

            // Sync templates
            if ($request->has('template_ids')) {
                $schedule->templates()->sync($request->template_ids ?? []);
            }

            // Sync conditions
            if ($request->has('condition_ids')) {
                $schedule->conditions()->sync($request->condition_ids ?? []);
            }

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Email schedule updated successfully',
                    'redirect' => url('admin/k12connections/manage-email-schedules')
                ]);
            }

            return redirect()->to('admin/k12connections/manage-email-schedules')
                           ->with('success', 'Email schedule updated successfully');

        } catch (Exception $e) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to update email schedule: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()
                           ->withInput()
                           ->with('error', 'Failed to update email schedule: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        try {
            $schedule = EmailScheduler::findOrFail($id);
            $schedule->delete();
            return response()->json(['success' => true, 'message' => 'Email schedule deleted successfully']);
        } catch (Exception $e) {
            return response()->json(['success' => false, 'message' => 'Failed to delete email schedule: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Format recipients array for display
     *
     * @param mixed $recipients
     * @return string
     */
    private function formatRecipients($recipients)
    {
        if (empty($recipients)) {
            return '<span class="text-muted">No recipients</span>';
        }
        $recipientsArray = is_array($recipients) ? $recipients : json_decode($recipients, true);
        if (!is_array($recipientsArray) || count($recipientsArray) === 0) {
            return '<span class="text-muted">No recipients</span>';
        }
        $displayRecipients = array_slice($recipientsArray, 0, 3);
        $recipientsText = implode(', ', $displayRecipients);
        if (count($recipientsArray) > 3) {
            $recipientsText .= ' <span class="text-muted">+' . (count($recipientsArray) - 3) . ' more</span>';
        }
        return $recipientsText;
    }

    /**
     * Generate status badge HTML
     *
     * @param string $status
     * @return string
     */
    private function generateStatusBadge($status)
    {
        $badgeClass = $status === 'enabled' ? 'success' : 'secondary';
        return '<span class="badge badge-' . $badgeClass . '">' . ucfirst($status) . '</span>';
    }

    /**
     * Get model fields for AJAX requests
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getModelFields(Request $request)
    {
        try {
            $modelKey = $request->get('model_key');
            $models = config('email_scheduler.models', []);

            if (!$modelKey || !isset($models[$modelKey])) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid model selected'
                ], 400);
            }

            $modelConfig = $models[$modelKey];
            $fields = $modelConfig['condition_fields'] ?? [];

            return response()->json([
                'success' => true,
                'fields' => $fields
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch model fields: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create condition via AJAX
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function createCondition(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'model_name' => 'required|string',
                'relation_name' => 'nullable|string',
                'field_name' => 'required|string',
                'operator' => 'required|string',
                'value' => 'required|string',
                'description' => 'nullable|string',
                'logical_operator' => 'required|in:AND,OR',
                'priority' => 'nullable|integer|min:0'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $condition = EmailCondition::create([
                'model_name' => $request->model_name,
                'relation_name' => $request->relation_name,
                'field_name' => $request->field_name,
                'operator' => $request->operator,
                'value' => $request->value,
                'description' => $request->description ?: $request->model_name . '.' . $request->field_name . ' ' . $request->operator . ' ' . $request->value,
                'priority' => $request->priority ?: 0,
                'logical_operator' => $request->logical_operator,
                'active' => true
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Condition created successfully',
                'condition' => [
                    'id' => $condition->id,
                    'text' => $condition->description,
                    'model_name' => $condition->model_name,
                    'field_name' => $condition->field_name,
                    'operator' => $condition->operator,
                    'value' => $condition->value,
                    'logical_operator' => $condition->logical_operator
                ]
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create condition: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get conditions for AJAX requests
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getConditions(Request $request)
    {
        try {
            $modelName = $request->get('model_name');
            $conditions = EmailCondition::where('active', true);

            if ($modelName) {
                $conditions->where('model_name', $modelName);
            }

            $conditions = $conditions->get();

            return response()->json([
                'success' => true,
                'conditions' => $conditions->map(function($condition) {
                    return [
                        'id' => $condition->id,
                        'text' => $condition->description ?? $condition->model_name . '.' . $condition->field_name . ' ' . $condition->operator . ' ' . $condition->value,
                        'model_name' => $condition->model_name,
                        'field_name' => $condition->field_name,
                        'operator' => $condition->operator,
                        'value' => $condition->value,
                        'logical_operator' => $condition->logical_operator
                    ];
                })
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch conditions: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test trigger an email schedule manually
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function testTrigger(Request $request, $id)
    {
        try {
            $schedule = EmailScheduler::with(['templates', 'conditions'])->findOrFail($id);

            if ($schedule->status !== 'enabled') {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot trigger disabled schedule'
                ], 400);
            }

            $emailService = new EmailSchedulerService();

            // For immediate trigger
            if ($schedule->event_name) {
                $context = $request->get('context', []);
                $emailService->triggerImmediate($schedule->event_name, $context);
            } else {
                // For scheduled trigger (test run)
                $emailService->runScheduled();
            }

            return response()->json([
                'success' => true,
                'message' => 'Email schedule triggered successfully'
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to trigger email schedule: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate action buttons HTML
     *
     * @param int $scheduleId
     * @return string
     */
    private function generateActionButtons($scheduleId)
    {
        $editUrl = url('admin/k12connections/manage-email-schedules/' . $scheduleId . '/edit');
        $deleteUrl = url('admin/k12connections/manage-email-schedules/' . $scheduleId);
        return '
            <a href="' . $editUrl . '" class="btn btn-outline-secondary btn-sm" title="Edit">
                <i class="fa fa-pencil" aria-hidden="true"></i>
            </a>
            <button class="btn btn-outline-danger btn-sm delete-schedule" data-id="' . $scheduleId . '" data-url="' . $deleteUrl . '" title="Delete">
                <i class="fa fa-trash" aria-hidden="true"></i>
            </button>
        ';
    }
}
