<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Predefined Events
    |--------------------------------------------------------------------------
    */
    'events' => [
        'user_created',
        'requirement_created',
        'requirement_invite',
        'requirement_proposal',
        'first_login',
        'class_created',
        'class_completed',
        'payment_received',
        'contract_signed',
    ],

    /*
    |--------------------------------------------------------------------------
    | Recipient Types (Global)
    |--------------------------------------------------------------------------
    */
    'recipient_types' => [
        'all_educator' => 'All Educators',
        'applicant' => 'Applicant related to event',
        'admin' => 'Admin user role (user:super_admin)',
        'all_schools' => 'All Schools',
        'school' => 'School related to event',
        'school_role' => 'School user with specific role (school_role:coordinator)',
    ],

    /*
    |--------------------------------------------------------------------------
    | Models Map with Metadata
    |--------------------------------------------------------------------------
    */
    'models' => [
        'school_user' => [
            'label' => 'School User',
            'class' => App\Models\v1\SchoolUser::class,
            'recipient_fields' => [
                'email' => 'School User',
            ],
            'condition_fields' => [
                'created_at' => [
                    'label' => 'Created At',
                    'type' => 'date',
                ],
                'status' => [
                    'label' => 'Status',
                    'type' => 'enum',
                    'options' => ['Active', 'In Active'],
                ],
                'must_reset_password' => [
                    'label' => 'Password Reset Required',
                    'type' => 'boolean',
                    'options' => ['Yes', 'No'],
                ],
            ],
        ],
        'onboarding_instructor' => [
            'label' => 'Onboarding Instructor',
            'class' => App\Models\v1\OnboardingInstructor::class,
            'condition_fields' => [
                'created_at' => [
                    'label' => 'Created At',
                    'type' => 'date',
                ],
                'user_status' => [
                    'label' => 'User Status',
                    'type' => 'enum',
                    'options' => ['pending', 'approved', 'rejected', 'under_review'],
                ],
                'email_verified_at' => [
                    'label' => 'Email Verified At',
                    'type' => 'date',
                ],
                'is_active' => [
                    'label' => 'Is Active',
                    'type' => 'boolean',
                    'options' => ['Yes', 'No'],
                ],
            ],
        ],
        'platform_school_requirements' => [
            'label' => 'School Requirements',
            'class' => App\Models\v1\PlatformSchoolRequirements::class,
            'condition_fields' => [
                'created_at' => [
                    'label' => 'Created At',
                    'type' => 'date',
                ],
                'start_date' => [
                    'label' => 'Start Date',
                    'type' => 'date',
                ],
                'end_date' => [
                    'label' => 'End Date',
                    'type' => 'date',
                ],
                'status' => [
                    'label' => 'Status',
                    'type' => 'enum',
                    'options' => ['draft', 'published', 'closed', 'cancelled'],
                ],
                'delivery_mode' => [
                    'label' => 'Delivery Mode',
                    'type' => 'enum',
                    'options' => ['online', 'in-person', 'hybrid'],
                ],
                'compensation_amount_min' => [
                    'label' => 'Min Compensation',
                    'type' => 'number',
                ],
                'compensation_amount_max' => [
                    'label' => 'Max Compensation',
                    'type' => 'number',
                ],
            ],
        ],
        'platform_school_invites' => [
            'label' => 'School Invites',
            'class' => App\Models\v1\PlatformSchoolInvites::class,
            'condition_fields' => [
                'created_at' => [
                    'label' => 'Created At',
                    'type' => 'date',
                ],
                'deadline_date' => [
                    'label' => 'Deadline Date',
                    'type' => 'date',
                ],
                'status' => [
                    'label' => 'Status',
                    'type' => 'enum',
                    'options' => ['pending', 'accepted', 'rejected', 'expired'],
                ],
            ],
        ],
        'user' => [
            'label' => 'User',
            'class' => App\User::class,
            'condition_fields' => [
                'created_at' => [
                    'label' => 'Created At',
                    'type' => 'date',
                ],
                'email_verified_at' => [
                    'label' => 'Email Verified At',
                    'type' => 'date',
                ],
                'status' => [
                    'label' => 'Status',
                    'type' => 'enum',
                    'options' => ['active', 'inactive', 'suspended'],
                ],
                'user_type' => [
                    'label' => 'User Type',
                    'type' => 'enum',
                    'options' => ['admin', 'school', 'instructor'],
                ],
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Default Operators
    |--------------------------------------------------------------------------
    */
    'operators' => [
        '=' => 'Equals',
        '!=' => 'Not Equals',
        '>' => 'Greater than',
        '<' => 'Less than',
        '>=' => 'Greater than or equal',
        '<=' => 'Less than or equal',
        'in' => 'In array / relation',
        'not_in' => 'Not in array / relation',
    ],

    /*
    |--------------------------------------------------------------------------
    | Logical Operators
    |--------------------------------------------------------------------------
    */
    'logical_operators' => [
        'AND' => 'AND',
        'OR' => 'OR',
    ],

    /*
    |--------------------------------------------------------------------------
    | Recurrence Options
    |--------------------------------------------------------------------------
    */
    'recurrence' => [
        'daily' => 'Daily',
        'weekly' => 'Weekly',
        'monthly' => 'Monthly',
        'yearly' => 'Yearly',
    ],

];
