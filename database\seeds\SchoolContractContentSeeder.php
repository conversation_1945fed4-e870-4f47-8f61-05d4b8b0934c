<?php

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SchoolContractContentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Check if School Contract content already exists
        $existingContent = DB::table('tbl_content_settings')
            ->where('type', 'School Contract')
            ->where('locale', 'en')
            ->first();

        if (!$existingContent) {
            DB::table('tbl_content_settings')->insert([
                'type' => 'School Contract',
                'description' => '<h2>School Contract Template</h2>
                <p>This is the default school contract template. Please customize this content according to your requirements.</p>

                <h3>Contract Terms</h3>
                <p>Please specify the terms and conditions for the school contract here.</p>

                <h3>Educator Responsibilities</h3>
                <ul>
                    <li>Provide quality education services</li>
                    <li>Maintain professional standards</li>
                    <li>Follow school policies and procedures</li>
                </ul>

                <h3>School Responsibilities</h3>
                <ul>
                    <li>Provide necessary resources</li>
                    <li>Ensure timely payments</li>
                    <li>Maintain communication</li>
                </ul>

                <h3>Payment Terms</h3>
                <p>Payment terms and conditions will be specified here.</p>

                <h3>Duration</h3>
                <p>Contract duration and renewal terms.</p>',
                'document' => null,
                'locale' => 'en',
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }
}
