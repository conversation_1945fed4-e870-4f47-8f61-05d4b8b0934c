# Email Schedule CRUD System

## Overview

This document describes the complete CRUD (Create, Read, Update, Delete) implementation for the Email Schedule system in the Whizara application. The system allows administrators to create, manage, and trigger email schedules with conditions and templates.

## Features Implemented

### 1. Complete CRUD Operations
- **Create**: Add new email schedules with templates and conditions
- **Read**: View email schedule details and list all schedules
- **Update**: Edit existing email schedules
- **Delete**: Remove email schedules with confirmation

### 2. Email Schedule Configuration
- **Basic Information**: Name, status (enabled/disabled)
- **Trigger Types**: 
  - Immediate (event-based): Triggered by specific events
  - Scheduled (recurring): Daily, weekly, monthly, yearly
- **Recipients**: TO and CC recipients with predefined types
- **Templates**: Multiple email templates can be attached
- **Conditions**: Complex conditions for email triggering

### 3. Integration with EmailSchedulerService
- Proper integration with the existing `EmailSchedulerService`
- Support for immediate and scheduled triggers
- Condition evaluation and email dispatching

## Files Created/Modified

### Controllers
- `app/Http/Controllers/Admin/ManageEmailScheduleController.php` - Complete CRUD implementation

### Views
- `resources/views/admin/marketplace/email-schedules/create.blade.php` - Create form
- `resources/views/admin/marketplace/email-schedules/edit.blade.php` - Edit form  
- `resources/views/admin/marketplace/email-schedules/show.blade.php` - Detail view
- `resources/views/admin/marketplace/email-schedules/index.blade.php` - List view (existing)

### Routes
- Added routes for conditions API and test trigger functionality

### Database Seeders
- `database/seeds/EmailScheduleSeeder.php` - Sample data for testing

## Database Structure

The system uses the following tables:
- `email_schedules` - Main schedule configuration
- `email_templates` - Email templates
- `email_conditions` - Conditional logic
- `email_schedules_template` - Many-to-many relationship
- `email_schedule_conditions` - Many-to-many relationship

## Configuration

The system uses `config/email_scheduler.php` for:
- Available events
- Recipient types
- Recurrence options
- Operators and logical operators
- Model mappings

## Usage Instructions

### 1. Setup and Installation

1. Run the email schedule seeder:
```bash
php artisan db:seed --class=EmailScheduleSeeder
```

2. Ensure the following routes are accessible:
- `/admin/k12connections/manage-email-schedules` - Main index
- `/admin/k12connections/manage-email-schedules/create` - Create form
- `/admin/k12connections/manage-email-schedules/{id}/edit` - Edit form
- `/admin/k12connections/manage-email-schedules/{id}` - Show details

### 2. Creating Email Schedules

1. Navigate to Email Schedules section
2. Click "Add Schedule"
3. Fill in the form:
   - **Name**: Descriptive name for the schedule
   - **Status**: Enable or disable the schedule
   - **Recurrence**: Choose trigger frequency
   - **Event Name**: For immediate triggers only
   - **Recipients**: Select TO and CC recipients
   - **Templates**: Choose email templates to send
   - **Conditions**: Add conditional logic

### 3. Managing Schedules

- **View**: Click on any schedule to see full details
- **Edit**: Use the edit button to modify schedules
- **Delete**: Use the delete button with confirmation
- **Test Trigger**: Use the test button to manually trigger emails

### 4. Testing Email Schedules

The system includes a test trigger feature:
1. Go to the schedule detail page
2. Click "Test Trigger" button
3. The system will attempt to send emails based on the schedule configuration

## API Endpoints

### CRUD Operations
- `GET /admin/k12connections/manage-email-schedules` - List schedules
- `GET /admin/k12connections/manage-email-schedules/create` - Create form
- `POST /admin/k12connections/manage-email-schedules` - Store new schedule
- `GET /admin/k12connections/manage-email-schedules/{id}` - Show schedule
- `GET /admin/k12connections/manage-email-schedules/{id}/edit` - Edit form
- `PUT /admin/k12connections/manage-email-schedules/{id}` - Update schedule
- `DELETE /admin/k12connections/manage-email-schedules/{id}` - Delete schedule

### Additional Endpoints
- `GET /admin/k12connections/manage-email-schedules/conditions/get` - Get conditions via AJAX
- `POST /admin/k12connections/manage-email-schedules/{id}/test-trigger` - Test trigger schedule

## Frontend Features

### Form Validation
- Client-side validation with real-time feedback
- Server-side validation with error display
- AJAX form submission with loading states

### User Experience
- Select2 dropdowns for better UX
- Dynamic form fields based on selections
- Confirmation dialogs for destructive actions
- Success/error notifications using Alertify

### Responsive Design
- Bootstrap 4 compatible
- Mobile-friendly forms and tables
- Consistent with existing admin theme

## Integration Points

### EmailSchedulerService
The CRUD system integrates with the existing `EmailSchedulerService`:
- Immediate triggers via `triggerImmediate()` method
- Scheduled triggers via `runScheduled()` method
- Condition evaluation and email dispatching

### Configuration System
Uses the `email_scheduler.php` config file for:
- Event definitions
- Recipient type mappings
- Operator definitions
- Model configurations

## Error Handling

- Comprehensive try-catch blocks
- User-friendly error messages
- Proper HTTP status codes
- Logging of errors for debugging

## Security Considerations

- CSRF protection on all forms
- Input validation and sanitization
- Authorization checks (admin only)
- SQL injection prevention via Eloquent ORM

## Testing

### Manual Testing
1. Create a new email schedule
2. Edit an existing schedule
3. View schedule details
4. Test trigger functionality
5. Delete a schedule

### Sample Data
Use the provided seeder to create test data:
- Sample layouts and templates
- Example conditions
- Various schedule configurations

## Troubleshooting

### Common Issues
1. **Templates not loading**: Check if EmailTemplate model exists and has data
2. **Conditions not working**: Verify EmailCondition model and config
3. **Emails not sending**: Check EmailSchedulerService and mail configuration
4. **Routes not working**: Ensure routes are properly registered

### Debug Steps
1. Check Laravel logs for errors
2. Verify database connections
3. Test individual components
4. Use browser developer tools for AJAX issues

## Future Enhancements

Potential improvements:
1. Email preview functionality
2. Schedule analytics and reporting
3. Advanced condition builder UI
4. Email template editor
5. Bulk operations
6. Schedule history tracking

## Support

For issues or questions:
1. Check Laravel logs
2. Review configuration files
3. Test with sample data
4. Verify all dependencies are installed
