<?php

use App\Models\v1\EmailScheduler;
use App\Models\v1\EmailTemplate;
use App\Models\v1\EmailCondition;
use App\Models\v1\EmailLayout;
use Illuminate\Database\Seeder;

class EmailScheduleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Create sample email layout if not exists
        $layout = EmailLayout::firstOrCreate([
            'slug' => 'default-email-layout'
        ], [
            'title' => 'Default Email Layout',
            'header_html' => '<div style="background: #f8f9fa; padding: 20px; text-align: center;"><h1>Whizara</h1></div>',
            'footer_html' => '<div style="background: #f8f9fa; padding: 20px; text-align: center; margin-top: 20px;"><p>&copy; 2025 Whizara. All rights reserved.</p></div>',
            'tags' => ['default', 'system'],
            'created_by' => 1,
        ]);

        // Create sample email templates
        $welcomeTemplate = EmailTemplate::firstOrCreate([
            'slug' => 'welcome-email-template'
        ], [
            'title' => 'Welcome Email Template',
            'layout_id' => $layout->id,
            'subject' => 'Welcome to Whizara!',
            'body_html' => '<div style="padding: 20px;"><h2>Welcome {{name}}!</h2><p>Thank you for joining our platform. We are excited to have you on board.</p><p>Best regards,<br>The Whizara Team</p></div>',
            'tags' => ['welcome', 'user'],
            'created_by' => 1,
        ]);

        $reminderTemplate = EmailTemplate::firstOrCreate([
            'slug' => 'reminder-email-template'
        ], [
            'title' => 'Reminder Email Template',
            'layout_id' => $layout->id,
            'subject' => 'Reminder: {{subject}}',
            'body_html' => '<div style="padding: 20px;"><h2>Reminder</h2><p>This is a friendly reminder about: {{message}}</p><p>Please take action as needed.</p><p>Best regards,<br>The Whizara Team</p></div>',
            'tags' => ['reminder', 'notification'],
            'created_by' => 1,
        ]);

        // Create sample email conditions
        $userCreatedCondition = EmailCondition::firstOrCreate([
            'model_name' => 'User',
            'field_name' => 'created_at',
            'operator' => '>=',
            'value' => '2025-01-01'
        ], [
            'relation_name' => null,
            'description' => 'User created after January 1, 2025',
            'priority' => 1,
            'logical_operator' => 'AND',
            'active' => true,
        ]);

        $activeUserCondition = EmailCondition::firstOrCreate([
            'model_name' => 'User',
            'field_name' => 'status',
            'operator' => '=',
            'value' => 'active'
        ], [
            'relation_name' => null,
            'description' => 'User is active',
            'priority' => 2,
            'logical_operator' => 'AND',
            'active' => true,
        ]);

        // Create sample email schedules
        $welcomeSchedule = EmailScheduler::firstOrCreate([
            'name' => 'Welcome Email Schedule'
        ], [
            'status' => 'enabled',
            'recurrence' => 'once',
            'event_name' => 'user_created',
            'recipients_to' => ['all_educator'],
            'recipients_cc' => ['admin'],
            'created_by' => 1,
        ]);

        // Attach templates and conditions to the schedule
        $welcomeSchedule->templates()->syncWithoutDetaching([$welcomeTemplate->id]);
        $welcomeSchedule->conditions()->syncWithoutDetaching([$userCreatedCondition->id, $activeUserCondition->id]);

        $dailyReminderSchedule = EmailScheduler::firstOrCreate([
            'name' => 'Daily Reminder Schedule'
        ], [
            'status' => 'enabled',
            'recurrence' => 'daily',
            'event_name' => null,
            'recipients_to' => ['all_schools'],
            'recipients_cc' => [],
            'created_by' => 1,
        ]);

        // Attach templates to the daily reminder schedule
        $dailyReminderSchedule->templates()->syncWithoutDetaching([$reminderTemplate->id]);

        $weeklyReportSchedule = EmailScheduler::firstOrCreate([
            'name' => 'Weekly Report Schedule'
        ], [
            'status' => 'disabled',
            'recurrence' => 'weekly',
            'event_name' => null,
            'recipients_to' => ['admin'],
            'recipients_cc' => ['all_educator'],
            'created_by' => 1,
        ]);

        // Attach templates to the weekly report schedule
        $weeklyReportSchedule->templates()->syncWithoutDetaching([$reminderTemplate->id]);

        echo "Email Schedule seeder completed successfully!\n";
        echo "Created:\n";
        echo "- 1 Email Layout\n";
        echo "- 2 Email Templates\n";
        echo "- 2 Email Conditions\n";
        echo "- 3 Email Schedules\n";
    }
}
