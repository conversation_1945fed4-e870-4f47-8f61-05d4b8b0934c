<?php
namespace App\Services;

use App\OnboardingInstructor as Educator;
use App\Models\PlatformSchoolRequirements as Requirement;
use App\Models\k12ConnectionClasses as Classes;
use App\Models\PlatformSchoolInvites as Invites;
use App\Models\SchoolReviewApplicants as Proposal;

use Illuminate\Support\Collection;

class EducatorInvitationService
{
    public function getEligibleEducators(Requirement $requirement, $searchTerm = []): Collection
    {
        $subjectMatch = $searchTerm['Subject'] ?? false;
        $availabilityMatch = $searchTerm['Availability'] ?? false;
        $locationMatch = $searchTerm['Location'] ?? false;
        $gradeMatch = $searchTerm['Grade'] ?? false;
        $deliveryModeMatch = $searchTerm['Format'] ?? false;
        $includeSubstitutes = $searchTerm['Sub'] ?? false;
        $includeCredentialed = $searchTerm['Credentialed'] ?? false;
        $includeSpecialEducation = $searchTerm['Special-Education'] ?? false;

        $educators = Educator::query()
            ->where('user_status', 'Active')
            ->with(['approvedBudget.lines.subject', 'availability', 'step3', 'onboardingFinalization', 'locations'])
            ->whereHas('approvedBudget', function($q) use ($requirement, $subjectMatch) {
                $q->whereHas('lines',  function($q) use ($requirement, $subjectMatch) {
                    $q->whereHas('subject', function($q) use ($requirement, $subjectMatch) {
                        if($subjectMatch) {
                            $q->where('subject_code', $requirement->subject->subject_code);
                        } else {
                            $q->where('subject_area_id', $requirement->subject->subject_area_id);
                        }
                    });
                });
            });
        if($deliveryModeMatch) {
            $educators->where('teach', $requirement->delivery_mode);
        }
        if($gradeMatch) {
            $educators->whereHas('step3', function($q) use ($requirement) {
                $grades = explode(',', $requirement->grade_levels_id);

                $q->where(function($query) use ($grades) {
                    foreach ($grades as $grade) {
                        $query->whereRaw("FIND_IN_SET(?, i_prefer_to_teach)", [$grade]);
                    }
                });
            });
        }

        if(!$includeSubstitutes) {
            $educators->whereHas('onboardingFinalization', function($q) use ($requirement) {
                $q->where('open_to_substitute_opportunity', 0);
            });
        }

        $educators = $educators->get();
        $educators = $educators->filter(function($educator) use ($requirement, $availabilityMatch, $locationMatch) {
            return (
                $availabilityMatch ? $this->passesAvailabilityCheck($educator, $requirement) : true
            ) && (
                $locationMatch ? $this->passesLocationCheck($educator, $requirement) : true
            );
        });

        return $educators;
    }

    function passesLocationCheck($educator, $requirement): bool
    {
        if ($requirement->delivery_mode == 'online') {
            return true; // skip location check for online
        }
        $distance = $this->calculateDistance($educator->locations->lat, $educator->locations->lng, $requirement->lat, $requirement->lng);
        return $distance <= $educator->radius_miles;
    }

    private function passesAvailabilityCheck($educator, $requirement): bool
    {
        foreach ($requirement->detailed_schedule as $day => $timeSlot) {
            try {
                foreach ($timeSlot as $time) {
                    if (! $educator->hasAvailability($day, $time['start_time'], $time['end_time'])) {
                        return false;
                    }
                }
            } catch (\Throwable $th) {
                dd($th, $timeSlot);
            }
        }
        return true;
    }

    private function calculateDistance($lat1, $lng1, $lat2, $lng2): float
    {
        $earthRadius = 3958.8; // miles
        $dLat = deg2rad($lat2 - $lat1);
        $dLng = deg2rad($lng2 - $lng1);

        $a = sin($dLat/2) ** 2 + cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * sin($dLng/2) ** 2;
        $c = 2 * atan2(sqrt($a), sqrt(1-$a));

        return $earthRadius * $c;
    }

    public function conflictingClasses($instructorId, $day, $start, $end)
    {
        return Classes::where(function($q) use ($instructorId) {
                $q->where(function($q) use ($instructorId) {
                    $q->where('main_instructor_id', $instructorId)
                      ->whereNull('sub_instructor_id');
                })->orWhere('sub_instructor_id', $instructorId);
            })
            ->where('day', $day)
            ->where(function ($query) use ($startTime, $endTime) {
                $query->whereBetween('start_time', [$startTime, $endTime])
                    ->orWhereBetween('end_time', [$startTime, $endTime])
                    ->orWhere(function ($q) use ($startTime, $endTime) {
                        $q->where('start_time', '<=', $startTime)
                            ->where('end_time', '>=', $endTime);
                    });
            });
    }

    function updateInviteStatus($instructor, $requirement, $status)
    {
        $invite = Invites::where([
            'school_id' => $requirement->school_id,
            'requirement_id' => $requirement->id,
            'user_id' => $instructor->id
        ])->first();

        if (!$invite) {
            return null;
        }

        if ($invite->status == 'pending') {
            $invite->status = $status;
            $invite->save();
            return $invite;
        } else {
            return null;
        }
    }

    function addProposal($requirement, $instructor, $offerDescription, $inviteId = null)
    {
        // Check if contract already exists for this requirement
        $existingContract = \App\SchoolRequirementContract::where('requirement_id', $requirement->id)->first();
        if ($existingContract) {
            return null; // Cannot send proposal if contract already exists
        }

        $existingProposal = Proposal::where([
            'instructor_id' => $instructor->id,
            'school_id' => $requirement->school_id,
            'requirement_id' => $requirement->id,
        ])->first();

        if ($existingProposal) {
            return null;
        }
        if($inviteId) {
            $invite = $this->updateInviteStatus($instructor, $requirement, 'accepted');
            if(!$invite) {
                return null;
            }
        }

        $proposal = Proposal::create([
            'instructor_id' => $instructor->id,
            'school_id' => $requirement->school_id,
            'requirement_id' => $requirement->id,
            'offer_description' => $offerDescription,
            'invite_id' => $inviteId,
            'proposed_rate' => $this->calculateEducatorRate($requirement, $instructor),
            'status' => 1,
        ]);

        return $proposal;
    }

    function calculateEducatorRate($requirement, $educator): float
    {
        $gradeLevel = $educator->step3->i_prefer_to_teach ?? '';
        $lines = $educator->budgetLines; // already merged from all approved budgets

        $subjectCode   = $requirement->subject->subject_code;
        $subjectAreaId = $requirement->subject->subject_area_id;

        // First try exact subject code
        $budgetLine = $lines->firstWhere('subject_code', $subjectCode);

        // Fallback to subject area
        if (!$budgetLine) {
            $budgetLine = $lines
                ->filter(fn($line) => optional($line->subject)->subject_area_id == $subjectAreaId)
                ->sortBy(fn($line) => abs((int)$line->subject_code - (int)$subjectCode))
                ->first();
        }

        if (!$budgetLine) {
            return 0; // safe fallback
        }

        $totalHours   = $requirement->totalHours ?? $requirement->class_duration;
        $estimated_cost = $budgetLine->total * $totalHours;

        return $estimated_cost;
    }
}
