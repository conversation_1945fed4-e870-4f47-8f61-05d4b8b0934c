

<?php $__env->startSection('title'); ?>
    Email Schedules | Whizara
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <!-- MAIN SECTION START -->
    <main class="content">
        <div class="container-fluid p-0">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item" aria-current="page">Email Schedules</li>
                    <li class="breadcrumb-item">
                        <a href="<?php echo e(url('admin/k12connections/manage-email-schedules/create')); ?>">Add Schedule</a>
                    </li>
                </ol>
            </nav>

            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Email Schedules List</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table id="dataTable" class="table table-striped" style="width:100%">
                                    <thead class="thead-dark">
                                        <tr>
                                            <th>Name</th>
                                            <th>Recurrence</th>
                                            <th>Recipients To</th>
                                            <th>Status</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- Data will be loaded via DataTables AJAX -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    <!-- MAIN SECTION END -->
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<link rel="stylesheet" href="<?php echo e(asset('css/datatables.min.css')); ?>">
<script src="<?php echo e(asset('js/datatables.min.js')); ?>"></script>

<script>
$(function() {
    // Initialize DataTable
    if (typeof dataTable !== 'undefined' && dataTable instanceof $.fn.dataTable.Api) {
        dataTable.destroy();
    }

    window.dataTable = initializeAdminDataTable("#dataTable", "<?php echo e(url('admin/k12connections/manage-email-schedules')); ?>", [
        {
            data: 'name',
            name: 'name'
        },
        {
            data: 'recurrence',
            name: 'recurrence'
        },
        {
            data: 'recipients_to',
            name: 'recipients_to',
            orderable: false,
            searchable: false
        },
        {
            data: 'status',
            name: 'status',
            orderable: false,
            searchable: false
        },
        {
            data: 'action',
            name: 'action',
            orderable: false,
            searchable: false
        }
    ]);

    // Handle delete button click
    $(document).on('click', '.delete-schedule', function(e) {
        e.preventDefault();

        const scheduleId = $(this).data('id');
        const deleteUrl = $(this).data('url');

        if (confirm('Are you sure you want to delete this email schedule?')) {
            $.ajax({
                url: deleteUrl,
                type: 'DELETE',
                data: {
                    _token: $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    if (response.success) {
                        alertify.success(response.message || 'Email schedule deleted successfully');
                        window.dataTable.ajax.reload();
                    } else {
                        alertify.error(response.message || 'Failed to delete email schedule');
                    }
                },
                error: function(xhr) {
                    alertify.error('An error occurred while deleting the email schedule');
                }
            });
        }
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\whizara\whizara\resources\views/admin/marketplace/email-schedules/index.blade.php ENDPATH**/ ?>