<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class AddEducatorSelectedEnumToPlatformSchoolRequirementsStatus extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::statement("ALTER TABLE platform_school_requirements MODIFY COLUMN status ENUM('draft', 'open', 'filled', 'closed', 'completed', 'educator_selected') DEFAULT 'draft' COMMENT 'The current status of the requirement'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::statement("ALTER TABLE platform_school_requirements MODIFY COLUMN status ENUM('draft', 'open', 'filled', 'closed', 'completed') DEFAULT 'draft' COMMENT 'The current status of the requirement'");
    }
}
