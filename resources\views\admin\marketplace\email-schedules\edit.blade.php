@extends('admin.layouts.master')

@section('title')
    Edit Email Schedule | Whizara
@endsection
<style>
    .select2-container--bootstrap4 .select2-selection--multiple .select2-selection__choice{
        margin: 0.2rem 0.25rem 0.3rem 0.2rem !important;
    }
</style>
@section('content')
    <!-- MAIN SECTION START -->
    <main class="content">
        <div class="container-fluid p-0">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{{ url('admin/k12connections/manage-email-schedules') }}">Email Schedules</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">Edit Schedule</li>
                </ol>
            </nav>

            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Edit Email Schedule: {{ $schedule->name }}</h5>
                        </div>
                        <div class="card-body">
                            <form id="emailScheduleForm" method="POST" action="{{ url('admin/k12connections/manage-email-schedules/' . $schedule->id) }}">
                                @csrf
                                @method('PUT')

                                <!-- Basic Information -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="name">Schedule Name <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="name" name="name"
                                                   value="{{ old('name', $schedule->name) }}" required>
                                            @error('name')
                                                <div class="text-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="status">Status <span class="text-danger">*</span></label>
                                            <select class="form-control" id="status" name="status" required>
                                                <option value="enabled" {{ old('status', $schedule->status) == 'enabled' ? 'selected' : '' }}>Enabled</option>
                                                <option value="disabled" {{ old('status', $schedule->status) == 'disabled' ? 'selected' : '' }}>Disabled</option>
                                            </select>
                                            @error('status')
                                                <div class="text-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <!-- Trigger Configuration -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="recurrence">Recurrence <span class="text-danger">*</span></label>
                                            <select class="form-control" id="recurrence" name="recurrence" required>
                                                @foreach($recurrenceOptions as $key => $label)
                                                    <option value="{{ $key }}" {{ old('recurrence', $schedule->recurrence) == $key ? 'selected' : '' }}>
                                                        {{ $label }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            @error('recurrence')
                                                <div class="text-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="event_name">Event Name</label>
                                            <select class="form-control" id="event_name" name="event_name">
                                                <option value="">Select Event (for immediate trigger)</option>
                                                @foreach($events as $event)
                                                    <option value="{{ $event }}" {{ old('event_name', $schedule->event_name) == $event ? 'selected' : '' }}>
                                                        {{ ucwords(str_replace('_', ' ', $event)) }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            @error('event_name')
                                                <div class="text-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <!-- Recipients -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="recipients_to">Recipients (To)</label>
                                            <select class="form-control select2" id="recipients_to" name="recipients_to[]" multiple>
                                                @foreach($recipientTypes as $key => $label)
                                                    <option value="{{ $key }}"
                                                        {{ in_array($key, old('recipients_to', $schedule->recipients_to ?? [])) ? 'selected' : '' }}>
                                                        {{ $label }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            @error('recipients_to')
                                                <div class="text-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="recipients_cc">Recipients (CC)</label>
                                            <select class="form-control select2" id="recipients_cc" name="recipients_cc[]" multiple>
                                                @foreach($recipientTypes as $key => $label)
                                                    <option value="{{ $key }}"
                                                        {{ in_array($key, old('recipients_cc', $schedule->recipients_cc ?? [])) ? 'selected' : '' }}>
                                                        {{ $label }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            @error('recipients_cc')
                                                <div class="text-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <!-- Templates -->
                                <div class="row">
                                    <div class="col-12">
                                        <div class="form-group">
                                            <label for="template_ids">Email Templates</label>
                                            <select class="form-control select2" id="template_ids" name="template_ids[]" multiple>
                                                @foreach($templates as $template)
                                                    <option value="{{ $template->id }}"
                                                        {{ in_array($template->id, old('template_ids', $schedule->templates->pluck('id')->toArray())) ? 'selected' : '' }}>
                                                        {{ $template->title }} ({{ $template->subject }})
                                                    </option>
                                                @endforeach
                                            </select>
                                            @error('template_ids')
                                                <div class="text-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <!-- Conditions Section -->
                                <div class="row">
                                    <div class="col-12">
                                        <h6 class="text-muted mb-3">Conditions</h6>

                                        <!-- Existing Conditions -->
                                        <div class="form-group">
                                            <label for="condition_ids">Select Existing Conditions</label>
                                            <select class="form-control select2" id="condition_ids" name="condition_ids[]" multiple>
                                                @foreach($conditions as $condition)
                                                    <option value="{{ $condition->id }}"
                                                        {{ in_array($condition->id, old('condition_ids', $schedule->conditions->pluck('id')->toArray())) ? 'selected' : '' }}>
                                                        {{ $condition->description ?? $condition->model_name . '.' . $condition->field_name . ' ' . $condition->operator . ' ' . $condition->value }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            @error('condition_ids')
                                                <div class="text-danger">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <!-- Create New Conditions -->
                                        <div class="card mt-3">
                                            <div class="card-header d-flex justify-content-between align-items-center">
                                                <h6 class="mb-0">Create New Conditions</h6>
                                                <button type="button" class="btn btn-sm btn-success" id="addConditionRowBtn">
                                                    <i class="fa fa-plus"></i> Add More
                                                </button>
                                            </div>
                                            <div class="card-body">
                                                <div id="conditionRowsContainer">
                                                    <!-- Condition rows will be added here -->
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <button type="submit" class="btn btn-primary">Update Schedule</button>
                                    <a href="{{ url('admin/k12connections/manage-email-schedules') }}" class="btn btn-secondary">Cancel</a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    <!-- MAIN SECTION END -->
@endsection

@section('scripts')
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<style>
.condition-row {
    background-color: #f8f9fa;
    transition: all 0.3s ease;
}

.condition-row:hover {
    background-color: #e9ecef;
}

.condition-row.border-success {
    border-color: #28a745 !important;
    background-color: #d4edda;
}

.condition-row.border-danger {
    border-color: #dc3545 !important;
    background-color: #f8d7da;
}

.condition-row .form-control-sm {
    font-size: 0.875rem;
}

.condition-row .small {
    font-size: 0.75rem;
    font-weight: 500;
}

.condition-row .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

#conditionRowsContainer {
    max-height: 500px;
    overflow-y: auto;
}

.card-header .btn-sm {
    padding: 0.25rem 0.75rem;
}
</style>

<script>
$(document).ready(function() {
    // Initialize Select2
    $('.select2').select2({
        theme: 'bootstrap4',
        width: '100%',
        placeholder: 'Select options...',
        allowClear: true
    });

    // Handle form submission
    $('#emailScheduleForm').on('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const submitBtn = $(this).find('button[type="submit"]');
        const originalText = submitBtn.text();

        submitBtn.prop('disabled', true).text('Updating...');

        $.ajax({
            url: $(this).attr('action'),
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    alertify.success(response.message);
                    if (response.redirect) {
                        window.location.href = response.redirect;
                    }
                } else {
                    alertify.error(response.message || 'Failed to update email schedule');
                }
            },
            error: function(xhr) {
                let message = 'An error occurred';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                    const errors = Object.values(xhr.responseJSON.errors).flat();
                    message = errors.join(', ');
                }
                alertify.error(message);
            },
            complete: function() {
                submitBtn.prop('disabled', false).text(originalText);
            }
        });
    });

    // Show/hide event field based on recurrence
    $('#recurrence').on('change', function() {
        const recurrence = $(this).val();
        const eventField = $('#event_name').closest('.form-group');

        if (recurrence === 'once') {
            eventField.show();
        } else {
            eventField.hide();
            $('#event_name').val('');
        }
    }).trigger('change');

    // Condition management variables
    let conditionRowCounter = 0;
    let createdConditions = [];

    // Function to create a new condition row
    function createConditionRow(data = {}) {
        conditionRowCounter++;
        const rowId = `condition-row-${conditionRowCounter}`;

        const modelOptions = @json($models);
        const operators = @json($operators);
        const logicalOperators = @json($logicalOperators);

        let modelSelectHtml = '<option value="">Select Model</option>';
        Object.keys(modelOptions).forEach(key => {
            const selected = data.model_key === key ? 'selected' : '';
            modelSelectHtml += `<option value="${key}" ${selected}>${modelOptions[key].label || key.replace(/_/g, ' ')}</option>`;
        });

        let operatorSelectHtml = '<option value="">Select Operator</option>';
        Object.keys(operators).forEach(key => {
            const selected = data.operator === key ? 'selected' : '';
            operatorSelectHtml += `<option value="${key}" ${selected}>${operators[key]}</option>`;
        });

        let logicalSelectHtml = '';
        Object.keys(logicalOperators).forEach(key => {
            const selected = (data.logical_operator === key) || (!data.logical_operator && key === 'AND') ? 'selected' : '';
            logicalSelectHtml += `<option value="${key}" ${selected}>${logicalOperators[key]}</option>`;
        });

        const rowHtml = `
            <div class="condition-row border rounded p-3 mb-3" id="${rowId}" data-condition-id="${data.id || ''}">
                <div class="row">
                    <div class="col-md-2">
                        <div class="form-group mb-2">
                            <label class="small text-muted">Model <span class="text-danger">*</span></label>
                            <select class="form-control form-control-sm condition-model" name="conditions[${conditionRowCounter}][model_key]">
                                ${modelSelectHtml}
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group mb-2">
                            <label class="small text-muted">Relation</label>
                            <input type="text" class="form-control form-control-sm condition-relation"
                                   name="conditions[${conditionRowCounter}][relation_name]"
                                   value="${data.relation_name || ''}" placeholder="Optional">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group mb-2">
                            <label class="small text-muted">Field <span class="text-danger">*</span></label>
                            <select class="form-control form-control-sm condition-field"
                                    name="conditions[${conditionRowCounter}][field_name]" disabled>
                                <option value="">Select Field</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-1">
                        <div class="form-group mb-2">
                            <label class="small text-muted">Operator <span class="text-danger">*</span></label>
                            <select class="form-control form-control-sm condition-operator"
                                    name="conditions[${conditionRowCounter}][operator]">
                                ${operatorSelectHtml}
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group mb-2">
                            <label class="small text-muted">Value <span class="text-danger">*</span></label>
                            <div class="condition-value-container">
                                <input type="text" class="form-control form-control-sm condition-value"
                                       name="conditions[${conditionRowCounter}][value]"
                                       value="${data.value || ''}" placeholder="Enter value">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-1">
                        <div class="form-group mb-2">
                            <label class="small text-muted">Logic</label>
                            <select class="form-control form-control-sm condition-logical"
                                    name="conditions[${conditionRowCounter}][logical_operator]">
                                ${logicalSelectHtml}
                            </select>
                        </div>
                    </div>
                    <div class="col-md-1">
                        <div class="form-group mb-2">
                            <label class="small text-muted">Description</label>
                            <input type="text" class="form-control form-control-sm condition-description"
                                   name="conditions[${conditionRowCounter}][description]"
                                   value="${data.description || ''}" placeholder="Optional">
                        </div>
                    </div>
                    <div class="col-md-1">
                        <div class="form-group mb-2">
                            <label class="small text-muted">&nbsp;</label>
                            <div class="d-flex">
                                <button type="button" class="btn btn-sm btn-success save-condition mr-1" title="Save">
                                    <i class="fa fa-check"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-danger remove-condition" title="Remove">
                                    <i class="fa fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <input type="hidden" class="condition-id" name="conditions[${conditionRowCounter}][id]" value="${data.id || ''}">
            </div>
        `;

        return rowHtml;
    }

    // Add initial condition row
    function addConditionRow(data = {}) {
        const rowHtml = createConditionRow(data);
        $('#conditionRowsContainer').append(rowHtml);

        // If data is provided, load the fields for the model
        if (data.model_key && data.field_name) {
            const row = $(`#condition-row-${conditionRowCounter}`);
            loadModelFields(row, data.model_key, data.field_name);
        }
    }

    // Add initial empty row
    addConditionRow();

    // Handle add more button
    $('#addConditionRowBtn').on('click', function() {
        addConditionRow();
    });

    // Function to load model fields
    function loadModelFields(row, modelKey, selectedField = '') {
        const fieldSelect = row.find('.condition-field');

        fieldSelect.empty().append('<option value="">Select Field</option>');

        if (modelKey) {
            fieldSelect.prop('disabled', true);

            $.ajax({
                url: '{{ url("admin/k12connections/manage-email-schedules/model-fields/get") }}',
                type: 'GET',
                data: { model_key: modelKey },
                success: function(response) {
                    if (response.success && response.fields) {
                        $.each(response.fields, function(fieldKey, fieldConfig) {
                            const selected = selectedField === fieldKey ? 'selected' : '';
                            fieldSelect.append(`<option value="${fieldKey}" data-type="${fieldConfig.type}" ${selected}>${fieldConfig.label}</option>`);
                        });
                        fieldSelect.prop('disabled', false);

                        // If a field was selected, update the value input
                        if (selectedField) {
                            updateValueInput(row, response.fields[selectedField]);
                        }
                    }
                },
                error: function() {
                    alertify.error('Failed to load model fields');
                    fieldSelect.prop('disabled', false);
                }
            });
        } else {
            fieldSelect.prop('disabled', true);
        }
    }

    // Function to update value input based on field type
    function updateValueInput(row, fieldConfig, currentValue = '') {
        const valueContainer = row.find('.condition-value-container');
        const inputName = row.find('.condition-value').attr('name');

        let inputHtml = '';

        switch (fieldConfig.type) {
            case 'date':
                inputHtml = `<input type="date" class="form-control form-control-sm condition-value" name="${inputName}" value="${currentValue}">`;
                break;
            case 'number':
                inputHtml = `<input type="number" class="form-control form-control-sm condition-value" name="${inputName}" value="${currentValue}" placeholder="Enter number">`;
                break;
            case 'boolean':
                inputHtml = `<select class="form-control form-control-sm condition-value" name="${inputName}">
                    <option value="">Select Value</option>
                    <option value="1" ${currentValue == '1' ? 'selected' : ''}>Yes</option>
                    <option value="0" ${currentValue == '0' ? 'selected' : ''}>No</option>
                </select>`;
                break;
            case 'enum':
                inputHtml = `<select class="form-control form-control-sm condition-value" name="${inputName}">
                    <option value="">Select Value</option>`;

                if (fieldConfig.options) {
                    fieldConfig.options.forEach(function(option) {
                        const selected = currentValue === option ? 'selected' : '';
                        inputHtml += `<option value="${option}" ${selected}>${option}</option>`;
                    });
                }

                inputHtml += '</select>';
                break;
            default:
                inputHtml = `<input type="text" class="form-control form-control-sm condition-value" name="${inputName}" value="${currentValue}" placeholder="Enter value">`;
        }

        valueContainer.html(inputHtml);
    }

    // Handle model selection change
    $(document).on('change', '.condition-model', function() {
        const row = $(this).closest('.condition-row');
        const modelKey = $(this).val();

        if (modelKey) {
            loadModelFields(row, modelKey);
        } else {
            row.find('.condition-field').empty().append('<option value="">Select Field</option>').prop('disabled', true);
            row.find('.condition-value-container').html('<input type="text" class="form-control form-control-sm condition-value" placeholder="Enter value">');
        }
    });

    // Handle field selection change
    $(document).on('change', '.condition-field', function() {
        const row = $(this).closest('.condition-row');
        const selectedOption = $(this).find('option:selected');
        const fieldType = selectedOption.data('type');
        const modelKey = row.find('.condition-model').val();
        const fieldKey = $(this).val();
        const currentValue = row.find('.condition-value').val();

        if (fieldKey && modelKey) {
            // Get field config to update value input
            $.ajax({
                url: '{{ url("admin/k12connections/manage-email-schedules/model-fields/get") }}',
                type: 'GET',
                data: { model_key: modelKey },
                success: function(response) {
                    if (response.success && response.fields && response.fields[fieldKey]) {
                        updateValueInput(row, response.fields[fieldKey], currentValue);
                    }
                }
            });
        }
    });

    // Handle save condition button
    $(document).on('click', '.save-condition', function() {
        const row = $(this).closest('.condition-row');
        const model = row.find('.condition-model').val();
        const modelText = row.find('.condition-model option:selected').text();
        const relation = row.find('.condition-relation').val();
        const field = row.find('.condition-field').val();
        const fieldText = row.find('.condition-field option:selected').text();
        const operator = row.find('.condition-operator').val();
        const operatorText = row.find('.condition-operator option:selected').text();
        const value = row.find('.condition-value').val();
        const logical = row.find('.condition-logical').val();
        const description = row.find('.condition-description').val();
        const conditionId = row.find('.condition-id').val();

        // Validation
        if (!model || !field || !operator || !value) {
            alertify.error('Please fill in all required fields (Model, Field, Operator, Value)');
            return;
        }

        const btn = $(this);
        const originalHtml = btn.html();
        btn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i>');

        // Create condition via AJAX
        $.ajax({
            url: '{{ url("admin/k12connections/manage-email-schedules/conditions/create") }}',
            type: 'POST',
            data: {
                _token: $('meta[name="csrf-token"]').attr('content'),
                model_name: model,
                relation_name: relation,
                field_name: field,
                operator: operator,
                value: value,
                logical_operator: logical,
                description: description
            },
            success: function(response) {
                if (response.success) {
                    // Update the condition ID in the row
                    row.find('.condition-id').val(response.condition.id);
                    row.attr('data-condition-id', response.condition.id);

                    // Add to existing conditions dropdown
                    $('#condition_ids').append(`<option value="${response.condition.id}" selected>${response.condition.text}</option>`);
                    $('#condition_ids').trigger('change');

                    // Visual feedback - change row border to green temporarily
                    row.removeClass('border-danger').addClass('border-success');
                    setTimeout(() => {
                        row.removeClass('border-success').addClass('border');
                    }, 2000);

                    alertify.success('Condition saved successfully');
                } else {
                    alertify.error(response.message || 'Failed to save condition');
                }
            },
            error: function(xhr) {
                let message = 'Failed to save condition';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                    const errors = Object.values(xhr.responseJSON.errors).flat();
                    message = errors.join(', ');
                }
                alertify.error(message);

                // Visual feedback - change row border to red temporarily
                row.removeClass('border-success').addClass('border-danger');
                setTimeout(() => {
                    row.removeClass('border-danger').addClass('border');
                }, 3000);
            },
            complete: function() {
                btn.prop('disabled', false).html(originalHtml);
            }
        });
    });

    // Handle remove condition button
    $(document).on('click', '.remove-condition', function() {
        const row = $(this).closest('.condition-row');
        const conditionId = row.find('.condition-id').val();

        if (confirm('Are you sure you want to remove this condition?')) {
            // If condition was saved, remove from dropdown
            if (conditionId) {
                $(`#condition_ids option[value="${conditionId}"]`).remove();
                $('#condition_ids').trigger('change');
            }

            // Remove the row
            row.remove();

            alertify.success('Condition removed');
        }
    });

    // Function to create a new condition row
    function createConditionRow(data = {}) {
        conditionRowCounter++;
        const rowId = `condition-row-${conditionRowCounter}`;

        const modelOptions = @json($models);
        const operators = @json($operators);
        const logicalOperators = @json($logicalOperators);

        let modelSelectHtml = '<option value="">Select Model</option>';
        Object.keys(modelOptions).forEach(key => {
            const selected = data.model_key === key ? 'selected' : '';
            modelSelectHtml += `<option value="${key}" ${selected}>${modelOptions[key].label || key.replace(/_/g, ' ')}</option>`;
        });

        let operatorSelectHtml = '<option value="">Select Operator</option>';
        Object.keys(operators).forEach(key => {
            const selected = data.operator === key ? 'selected' : '';
            operatorSelectHtml += `<option value="${key}" ${selected}>${operators[key]}</option>`;
        });

        let logicalSelectHtml = '';
        Object.keys(logicalOperators).forEach(key => {
            const selected = (data.logical_operator === key) || (!data.logical_operator && key === 'AND') ? 'selected' : '';
            logicalSelectHtml += `<option value="${key}" ${selected}>${logicalOperators[key]}</option>`;
        });

        const rowHtml = `
            <div class="condition-row border rounded p-3 mb-3" id="${rowId}" data-condition-id="${data.id || ''}">
                <div class="row">
                    <div class="col-md-2">
                        <div class="form-group mb-2">
                            <label class="small text-muted">Model <span class="text-danger">*</span></label>
                            <select class="form-control form-control-sm condition-model" name="conditions[${conditionRowCounter}][model_key]">
                                ${modelSelectHtml}
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group mb-2">
                            <label class="small text-muted">Relation</label>
                            <input type="text" class="form-control form-control-sm condition-relation"
                                   name="conditions[${conditionRowCounter}][relation_name]"
                                   value="${data.relation_name || ''}" placeholder="Optional">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group mb-2">
                            <label class="small text-muted">Field <span class="text-danger">*</span></label>
                            <select class="form-control form-control-sm condition-field"
                                    name="conditions[${conditionRowCounter}][field_name]" disabled>
                                <option value="">Select Field</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-1">
                        <div class="form-group mb-2">
                            <label class="small text-muted">Operator <span class="text-danger">*</span></label>
                            <select class="form-control form-control-sm condition-operator"
                                    name="conditions[${conditionRowCounter}][operator]">
                                ${operatorSelectHtml}
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group mb-2">
                            <label class="small text-muted">Value <span class="text-danger">*</span></label>
                            <div class="condition-value-container">
                                <input type="text" class="form-control form-control-sm condition-value"
                                       name="conditions[${conditionRowCounter}][value]"
                                       value="${data.value || ''}" placeholder="Enter value">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-1">
                        <div class="form-group mb-2">
                            <label class="small text-muted">Logic</label>
                            <select class="form-control form-control-sm condition-logical"
                                    name="conditions[${conditionRowCounter}][logical_operator]">
                                ${logicalSelectHtml}
                            </select>
                        </div>
                    </div>
                    <div class="col-md-1">
                        <div class="form-group mb-2">
                            <label class="small text-muted">Description</label>
                            <input type="text" class="form-control form-control-sm condition-description"
                                   name="conditions[${conditionRowCounter}][description]"
                                   value="${data.description || ''}" placeholder="Optional">
                        </div>
                    </div>
                    <div class="col-md-1">
                        <div class="form-group mb-2">
                            <label class="small text-muted">&nbsp;</label>
                            <div class="d-flex">
                                <button type="button" class="btn btn-sm btn-success save-condition me-1" title="Save">
                                    <i class="fa fa-check"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-danger remove-condition" title="Remove">
                                    <i class="fa fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <input type="hidden" class="condition-id" name="conditions[${conditionRowCounter}][id]" value="${data.id || ''}">
            </div>
        `;

        return rowHtml;
    }

    // Add initial condition row
    function addConditionRow(data = {}) {
        const rowHtml = createConditionRow(data);
        $('#conditionRowsContainer').append(rowHtml);

        // If data is provided, load the fields for the model
        if (data.model_key && data.field_name) {
            const row = $(`#condition-row-${conditionRowCounter}`);
            loadModelFields(row, data.model_key, data.field_name);
        }
    }

    // Add initial empty row
    addConditionRow();

    // Handle add more button
    $('#addConditionRowBtn').on('click', function() {
        addConditionRow();
    });

    // Function to load model fields
    function loadModelFields(row, modelKey, selectedField = '') {
        const fieldSelect = row.find('.condition-field');

        fieldSelect.empty().append('<option value="">Select Field</option>');

        if (modelKey) {
            fieldSelect.prop('disabled', true);

            $.ajax({
                url: '{{ url("admin/k12connections/manage-email-schedules/model-fields/get") }}',
                type: 'GET',
                data: { model_key: modelKey },
                success: function(response) {
                    if (response.success && response.fields) {
                        $.each(response.fields, function(fieldKey, fieldConfig) {
                            const selected = selectedField === fieldKey ? 'selected' : '';
                            fieldSelect.append(`<option value="${fieldKey}" data-type="${fieldConfig.type}" ${selected}>${fieldConfig.label}</option>`);
                        });
                        fieldSelect.prop('disabled', false);

                        // If a field was selected, update the value input
                        if (selectedField) {
                            updateValueInput(row, response.fields[selectedField]);
                        }
                    }
                },
                error: function() {
                    alertify.error('Failed to load model fields');
                    fieldSelect.prop('disabled', false);
                }
            });
        } else {
            fieldSelect.prop('disabled', true);
        }
    }

    // Function to update value input based on field type
    function updateValueInput(row, fieldConfig, currentValue = '') {
        const valueContainer = row.find('.condition-value-container');
        const inputName = row.find('.condition-value').attr('name');

        let inputHtml = '';

        switch (fieldConfig.type) {
            case 'date':
                inputHtml = `<input type="date" class="form-control form-control-sm condition-value" name="${inputName}" value="${currentValue}">`;
                break;
            case 'number':
                inputHtml = `<input type="number" class="form-control form-control-sm condition-value" name="${inputName}" value="${currentValue}" placeholder="Enter number">`;
                break;
            case 'boolean':
                inputHtml = `<select class="form-control form-control-sm condition-value" name="${inputName}">
                    <option value="">Select Value</option>
                    <option value="1" ${currentValue == '1' ? 'selected' : ''}>Yes</option>
                    <option value="0" ${currentValue == '0' ? 'selected' : ''}>No</option>
                </select>`;
                break;
            case 'enum':
                inputHtml = `<select class="form-control form-control-sm condition-value" name="${inputName}">
                    <option value="">Select Value</option>`;

                if (fieldConfig.options) {
                    fieldConfig.options.forEach(function(option) {
                        const selected = currentValue === option ? 'selected' : '';
                        inputHtml += `<option value="${option}" ${selected}>${option}</option>`;
                    });
                }

                inputHtml += '</select>';
                break;
            default:
                inputHtml = `<input type="text" class="form-control form-control-sm condition-value" name="${inputName}" value="${currentValue}" placeholder="Enter value">`;
        }

        valueContainer.html(inputHtml);
    }

    // Handle model selection change
    $(document).on('change', '.condition-model', function() {
        const row = $(this).closest('.condition-row');
        const modelKey = $(this).val();

        if (modelKey) {
            loadModelFields(row, modelKey);
        } else {
            row.find('.condition-field').empty().append('<option value="">Select Field</option>').prop('disabled', true);
            row.find('.condition-value-container').html('<input type="text" class="form-control form-control-sm condition-value" placeholder="Enter value">');
        }
    });

    // Handle field selection change
    $(document).on('change', '.condition-field', function() {
        const row = $(this).closest('.condition-row');
        const selectedOption = $(this).find('option:selected');
        const fieldType = selectedOption.data('type');
        const modelKey = row.find('.condition-model').val();
        const fieldKey = $(this).val();
        const currentValue = row.find('.condition-value').val();

        if (fieldKey && modelKey) {
            // Get field config to update value input
            $.ajax({
                url: '{{ url("admin/k12connections/manage-email-schedules/model-fields/get") }}',
                type: 'GET',
                data: { model_key: modelKey },
                success: function(response) {
                    if (response.success && response.fields && response.fields[fieldKey]) {
                        updateValueInput(row, response.fields[fieldKey], currentValue);
                    }
                }
            });
        }
    });

    // Handle save condition button
    $(document).on('click', '.save-condition', function() {
        const row = $(this).closest('.condition-row');
        const model = row.find('.condition-model').val();
        const modelText = row.find('.condition-model option:selected').text();
        const relation = row.find('.condition-relation').val();
        const field = row.find('.condition-field').val();
        const fieldText = row.find('.condition-field option:selected').text();
        const operator = row.find('.condition-operator').val();
        const operatorText = row.find('.condition-operator option:selected').text();
        const value = row.find('.condition-value').val();
        const logical = row.find('.condition-logical').val();
        const description = row.find('.condition-description').val();
        const conditionId = row.find('.condition-id').val();

        // Validation
        if (!model || !field || !operator || !value) {
            alertify.error('Please fill in all required fields (Model, Field, Operator, Value)');
            return;
        }

        const btn = $(this);
        const originalHtml = btn.html();
        btn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i>');

        // Create condition via AJAX
        $.ajax({
            url: '{{ url("admin/k12connections/manage-email-schedules/conditions/create") }}',
            type: 'POST',
            data: {
                _token: $('meta[name="csrf-token"]').attr('content'),
                model_name: model,
                relation_name: relation,
                field_name: field,
                operator: operator,
                value: value,
                logical_operator: logical,
                description: description
            },
            success: function(response) {
                if (response.success) {
                    // Update the condition ID in the row
                    row.find('.condition-id').val(response.condition.id);
                    row.attr('data-condition-id', response.condition.id);

                    // Add to existing conditions dropdown
                    $('#condition_ids').append(`<option value="${response.condition.id}" selected>${response.condition.text}</option>`);
                    $('#condition_ids').trigger('change');

                    // Visual feedback - change row border to green temporarily
                    row.removeClass('border-danger').addClass('border-success');
                    setTimeout(() => {
                        row.removeClass('border-success').addClass('border');
                    }, 2000);

                    alertify.success('Condition saved successfully');
                } else {
                    alertify.error(response.message || 'Failed to save condition');
                }
            },
            error: function(xhr) {
                let message = 'Failed to save condition';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                    const errors = Object.values(xhr.responseJSON.errors).flat();
                    message = errors.join(', ');
                }
                alertify.error(message);

                // Visual feedback - change row border to red temporarily
                row.removeClass('border-success').addClass('border-danger');
                setTimeout(() => {
                    row.removeClass('border-danger').addClass('border');
                }, 3000);
            },
            complete: function() {
                btn.prop('disabled', false).html(originalHtml);
            }
        });
    });

    // Handle remove condition button
    $(document).on('click', '.remove-condition', function() {
        const row = $(this).closest('.condition-row');
        const conditionId = row.find('.condition-id').val();

        if (confirm('Are you sure you want to remove this condition?')) {
            // If condition was saved, remove from dropdown
            if (conditionId) {
                $(`#condition_ids option[value="${conditionId}"]`).remove();
                $('#condition_ids').trigger('change');
            }

            // Remove the row
            row.remove();

            alertify.success('Condition removed');
        }
    });

    // Handle add condition button
    let createdConditions = [];
    $('#addConditionBtn').on('click', function() {
        const model = $('#new_condition_model').val();
        const modelText = $('#new_condition_model option:selected').text();
        const relation = $('#new_condition_relation').val();
        const field = $('#new_condition_field').val();
        const fieldText = $('#new_condition_field option:selected').text();
        const operator = $('#new_condition_operator').val();
        const operatorText = $('#new_condition_operator option:selected').text();
        const value = $('#new_condition_value').val();
        const logical = $('#new_condition_logical').val();
        const description = $('#new_condition_description').val();

        // Validation
        if (!model || !field || !operator || !value) {
            alertify.error('Please fill in all required fields (Model, Field, Operator, Value)');
            return;
        }

        const btn = $(this);
        const originalText = btn.html();
        btn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Adding...');

        // Create condition via AJAX
        $.ajax({
            url: '{{ url("admin/k12connections/manage-email-schedules/conditions/create") }}',
            type: 'POST',
            data: {
                _token: $('meta[name="csrf-token"]').attr('content'),
                model_name: model,
                relation_name: relation,
                field_name: field,
                operator: operator,
                value: value,
                logical_operator: logical,
                description: description
            },
            success: function(response) {
                if (response.success) {
                    // Add to created conditions array
                    createdConditions.push(response.condition);

                    // Add to table
                    const tableBody = $('#createdConditionsTable tbody');
                    const row = `
                        <tr data-condition-id="${response.condition.id}">
                            <td>${modelText}</td>
                            <td>${fieldText}</td>
                            <td><code>${operator}</code></td>
                            <td>${value}</td>
                            <td>${logical}</td>
                            <td>${description || 'N/A'}</td>
                            <td>
                                <button type="button" class="btn btn-sm btn-danger remove-condition" data-id="${response.condition.id}">
                                    <i class="fa fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                    `;
                    tableBody.append(row);

                    // Show the table
                    $('#createdConditionsCard').show();

                    // Add to existing conditions dropdown
                    $('#condition_ids').append(`<option value="${response.condition.id}" selected>${response.condition.text}</option>`);
                    $('#condition_ids').trigger('change');

                    // Clear form
                    $('#new_condition_model').val('');
                    $('#new_condition_relation').val('');
                    $('#new_condition_field').empty().append('<option value="">Select Field</option>').prop('disabled', true);
                    $('#new_condition_operator').val('');
                    $('#new_condition_value').val('');
                    $('#new_condition_description').val('');

                    alertify.success(response.message);
                } else {
                    alertify.error(response.message || 'Failed to create condition');
                }
            },
            error: function(xhr) {
                let message = 'Failed to create condition';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                    const errors = Object.values(xhr.responseJSON.errors).flat();
                    message = errors.join(', ');
                }
                alertify.error(message);
            },
            complete: function() {
                btn.prop('disabled', false).html(originalText);
            }
        });
    });

    // Handle remove condition
    $(document).on('click', '.remove-condition', function() {
        const conditionId = $(this).data('id');
        const row = $(this).closest('tr');

        if (confirm('Are you sure you want to remove this condition?')) {
            // Remove from array
            createdConditions = createdConditions.filter(c => c.id != conditionId);

            // Remove from table
            row.remove();

            // Remove from dropdown
            $(`#condition_ids option[value="${conditionId}"]`).remove();
            $('#condition_ids').trigger('change');

            // Hide table if empty
            if ($('#createdConditionsTable tbody tr').length === 0) {
                $('#createdConditionsCard').hide();
            }

            alertify.success('Condition removed');
        }
    });
});
</script>
@endsection
