@extends('admin.layouts.master')

@section('title')
    Create Email Schedule | Whizara
@endsection

@section('content')
    <!-- MAIN SECTION START -->
    <main class="content">
        <div class="container-fluid p-0">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{{ url('admin/k12connections/manage-email-schedules') }}">Email Schedules</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">Create Schedule</li>
                </ol>
            </nav>

            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Create Email Schedule</h5>
                        </div>
                        <div class="card-body">
                            <form id="emailScheduleForm" method="POST" action="{{ url('admin/k12connections/manage-email-schedules') }}">
                                @csrf

                                <!-- Basic Information -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="name">Schedule Name <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="name" name="name"
                                                   value="{{ old('name') }}" required>
                                            @error('name')
                                                <div class="text-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="status">Status <span class="text-danger">*</span></label>
                                            <select class="form-control" id="status" name="status" required>
                                                <option value="enabled" {{ old('status') == 'enabled' ? 'selected' : '' }}>Enabled</option>
                                                <option value="disabled" {{ old('status') == 'disabled' ? 'selected' : '' }}>Disabled</option>
                                            </select>
                                            @error('status')
                                                <div class="text-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <!-- Trigger Configuration -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="recurrence">Recurrence <span class="text-danger">*</span></label>
                                            <select class="form-control" id="recurrence" name="recurrence" required>
                                                @foreach($recurrenceOptions as $key => $label)
                                                    <option value="{{ $key }}" {{ old('recurrence') == $key ? 'selected' : '' }}>
                                                        {{ $label }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            @error('recurrence')
                                                <div class="text-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="event_name">Event Name</label>
                                            <select class="form-control" id="event_name" name="event_name">
                                                <option value="">Select Event (for immediate trigger)</option>
                                                @foreach($events as $event)
                                                    <option value="{{ $event }}" {{ old('event_name') == $event ? 'selected' : '' }}>
                                                        {{ ucwords(str_replace('_', ' ', $event)) }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            @error('event_name')
                                                <div class="text-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <!-- Recipients -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="recipients_to">Recipients (To)</label>
                                            <select class="form-control select2" id="recipients_to" name="recipients_to[]" multiple>
                                                @foreach($recipientTypes as $key => $label)
                                                    <option value="{{ $key }}"
                                                        {{ in_array($key, old('recipients_to', [])) ? 'selected' : '' }}>
                                                        {{ $label }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            @error('recipients_to')
                                                <div class="text-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="recipients_cc">Recipients (CC)</label>
                                            <select class="form-control select2" id="recipients_cc" name="recipients_cc[]" multiple>
                                                @foreach($recipientTypes as $key => $label)
                                                    <option value="{{ $key }}"
                                                        {{ in_array($key, old('recipients_cc', [])) ? 'selected' : '' }}>
                                                        {{ $label }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            @error('recipients_cc')
                                                <div class="text-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <!-- Templates -->
                                <div class="row">
                                    <div class="col-12">
                                        <div class="form-group">
                                            <label for="template_ids">Email Templates</label>
                                            <select class="form-control select2" id="template_ids" name="template_ids[]" multiple>
                                                @foreach($templates as $template)
                                                    <option value="{{ $template->id }}"
                                                        {{ in_array($template->id, old('template_ids', [])) ? 'selected' : '' }}>
                                                        {{ $template->title }} ({{ $template->subject }})
                                                    </option>
                                                @endforeach
                                            </select>
                                            @error('template_ids')
                                                <div class="text-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <!-- Conditions Section -->
                                <div class="row">
                                    <div class="col-12">
                                        <h6 class="text-muted mb-3">Conditions</h6>

                                        <!-- Existing Conditions -->
                                        <div class="form-group">
                                            <label for="condition_ids">Select Existing Conditions</label>
                                            <select class="form-control select2" id="condition_ids" name="condition_ids[]" multiple>
                                                @foreach($conditions as $condition)
                                                    <option value="{{ $condition->id }}"
                                                        {{ in_array($condition->id, old('condition_ids', [])) ? 'selected' : '' }}>
                                                        {{ $condition->description ?? $condition->model_name . '.' . $condition->field_name . ' ' . $condition->operator . ' ' . $condition->value }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            @error('condition_ids')
                                                <div class="text-danger">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <!-- Create New Condition -->
                                        <div class="card mt-3">
                                            <div class="card-header">
                                                <h6 class="mb-0">Create New Condition</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-md-3">
                                                        <div class="form-group">
                                                            <label for="new_condition_model">Model Name <span class="text-danger">*</span></label>
                                                            <select class="form-control" id="new_condition_model" name="new_condition_model">
                                                                <option value="">Select Model</option>
                                                                @foreach($models as $key => $model)
                                                                    <option value="{{ $key }}">{{ $model['label'] ?? ucwords(str_replace('_', ' ', $key)) }}</option>
                                                                @endforeach
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-2">
                                                        <div class="form-group">
                                                            <label for="new_condition_relation">Relation</label>
                                                            <input type="text" class="form-control" id="new_condition_relation" name="new_condition_relation" placeholder="Optional">
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <div class="form-group">
                                                            <label for="new_condition_field">Field Name <span class="text-danger">*</span></label>
                                                            <select class="form-control" id="new_condition_field" name="new_condition_field" disabled>
                                                                <option value="">Select Field</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-2">
                                                        <div class="form-group">
                                                            <label for="new_condition_operator">Operator <span class="text-danger">*</span></label>
                                                            <select class="form-control" id="new_condition_operator" name="new_condition_operator">
                                                                <option value="">Select Operator</option>
                                                                @foreach($operators as $key => $label)
                                                                    <option value="{{ $key }}">{{ $label }}</option>
                                                                @endforeach
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-2">
                                                        <div class="form-group">
                                                            <label for="new_condition_logical">Logic</label>
                                                            <select class="form-control" id="new_condition_logical" name="new_condition_logical">
                                                                @foreach($logicalOperators as $key => $label)
                                                                    <option value="{{ $key }}">{{ $label }}</option>
                                                                @endforeach
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-md-4">
                                                        <div class="form-group">
                                                            <label for="new_condition_value">Value <span class="text-danger">*</span></label>
                                                            <div id="condition_value_container">
                                                                <input type="text" class="form-control" id="new_condition_value" name="new_condition_value" placeholder="Enter value">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <label for="new_condition_description">Description</label>
                                                            <input type="text" class="form-control" id="new_condition_description" name="new_condition_description" placeholder="Optional description">
                                                        </div>
                                                    </div>
                                                    <div class="col-md-2">
                                                        <div class="form-group">
                                                            <label>&nbsp;</label>
                                                            <button type="button" class="btn btn-success btn-block" id="addConditionBtn">
                                                                <i class="fa fa-plus"></i> Add
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Created Conditions Table -->
                                        <div class="card mt-3" id="createdConditionsCard" style="display: none;">
                                            <div class="card-header">
                                                <h6 class="mb-0">Created Conditions</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="table-responsive">
                                                    <table class="table table-sm" id="createdConditionsTable">
                                                        <thead>
                                                            <tr>
                                                                <th>Model</th>
                                                                <th>Field</th>
                                                                <th>Operator</th>
                                                                <th>Value</th>
                                                                <th>Logic</th>
                                                                <th>Description</th>
                                                                <th>Action</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody></tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <button type="submit" class="btn btn-primary">Create Schedule</button>
                                    <a href="{{ url('admin/k12connections/manage-email-schedules') }}" class="btn btn-secondary">Cancel</a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    <!-- MAIN SECTION END -->
@endsection

@section('scripts')
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<script>
$(document).ready(function() {
    // Initialize Select2
    $('.select2').select2({
        theme: 'bootstrap4',
        width: '100%',
        placeholder: 'Select options...',
        allowClear: true
    });

    // Handle form submission
    $('#emailScheduleForm').on('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const submitBtn = $(this).find('button[type="submit"]');
        const originalText = submitBtn.text();

        submitBtn.prop('disabled', true).text('Creating...');

        $.ajax({
            url: $(this).attr('action'),
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    alertify.success(response.message);
                    if (response.redirect) {
                        window.location.href = response.redirect;
                    }
                } else {
                    alertify.error(response.message || 'Failed to create email schedule');
                }
            },
            error: function(xhr) {
                let message = 'An error occurred';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                    const errors = Object.values(xhr.responseJSON.errors).flat();
                    message = errors.join(', ');
                }
                alertify.error(message);
            },
            complete: function() {
                submitBtn.prop('disabled', false).text(originalText);
            }
        });
    });

    // Show/hide event field based on recurrence
    $('#recurrence').on('change', function() {
        const recurrence = $(this).val();
        const eventField = $('#event_name').closest('.form-group');

        if (recurrence === 'once') {
            eventField.show();
        } else {
            eventField.hide();
            $('#event_name').val('');
        }
    }).trigger('change');

    // Handle model selection for condition creation
    $('#new_condition_model').on('change', function() {
        const modelKey = $(this).val();
        const fieldSelect = $('#new_condition_field');

        fieldSelect.empty().append('<option value="">Select Field</option>');

        if (modelKey) {
            fieldSelect.prop('disabled', true);

            $.ajax({
                url: '{{ url("admin/k12connections/manage-email-schedules/model-fields/get") }}',
                type: 'GET',
                data: { model_key: modelKey },
                success: function(response) {
                    if (response.success && response.fields) {
                        $.each(response.fields, function(fieldKey, fieldConfig) {
                            fieldSelect.append(`<option value="${fieldKey}" data-type="${fieldConfig.type}">${fieldConfig.label}</option>`);
                        });
                        fieldSelect.prop('disabled', false);
                    }
                },
                error: function() {
                    alertify.error('Failed to load model fields');
                    fieldSelect.prop('disabled', false);
                }
            });
        } else {
            fieldSelect.prop('disabled', true);
        }
    });

    // Handle field selection to update value input
    $('#new_condition_field').on('change', function() {
        const selectedOption = $(this).find('option:selected');
        const fieldType = selectedOption.data('type');
        const valueContainer = $('#condition_value_container');
        const currentValue = $('#new_condition_value').val();

        let inputHtml = '';

        switch (fieldType) {
            case 'date':
                inputHtml = `<input type="date" class="form-control" id="new_condition_value" name="new_condition_value" value="${currentValue}">`;
                break;
            case 'number':
                inputHtml = `<input type="number" class="form-control" id="new_condition_value" name="new_condition_value" value="${currentValue}" placeholder="Enter number">`;
                break;
            case 'boolean':
                inputHtml = `<select class="form-control" id="new_condition_value" name="new_condition_value">
                    <option value="">Select Value</option>
                    <option value="1" ${currentValue == '1' ? 'selected' : ''}>Yes</option>
                    <option value="0" ${currentValue == '0' ? 'selected' : ''}>No</option>
                </select>`;
                break;
            case 'enum':
                // Get enum options from the selected field
                const modelKey = $('#new_condition_model').val();
                const fieldKey = $(this).val();

                // Make AJAX call to get enum options
                $.ajax({
                    url: '{{ url("admin/k12connections/manage-email-schedules/model-fields/get") }}',
                    type: 'GET',
                    data: { model_key: modelKey },
                    success: function(response) {
                        if (response.success && response.fields && response.fields[fieldKey] && response.fields[fieldKey].options) {
                            let selectHtml = `<select class="form-control" id="new_condition_value" name="new_condition_value">
                                <option value="">Select Value</option>`;

                            response.fields[fieldKey].options.forEach(function(option) {
                                const selected = currentValue === option ? 'selected' : '';
                                selectHtml += `<option value="${option}" ${selected}>${option}</option>`;
                            });

                            selectHtml += '</select>';
                            valueContainer.html(selectHtml);
                        }
                    }
                });
                return; // Don't set default input
            default:
                inputHtml = `<input type="text" class="form-control" id="new_condition_value" name="new_condition_value" value="${currentValue}" placeholder="Enter value">`;
        }

        if (inputHtml) {
            valueContainer.html(inputHtml);
        }
    });

    // Handle add condition button
    let createdConditions = [];
    $('#addConditionBtn').on('click', function() {
        const model = $('#new_condition_model').val();
        const modelText = $('#new_condition_model option:selected').text();
        const relation = $('#new_condition_relation').val();
        const field = $('#new_condition_field').val();
        const fieldText = $('#new_condition_field option:selected').text();
        const operator = $('#new_condition_operator').val();
        const operatorText = $('#new_condition_operator option:selected').text();
        const value = $('#new_condition_value').val();
        const logical = $('#new_condition_logical').val();
        const description = $('#new_condition_description').val();

        // Validation
        if (!model || !field || !operator || !value) {
            alertify.error('Please fill in all required fields (Model, Field, Operator, Value)');
            return;
        }

        const btn = $(this);
        const originalText = btn.html();
        btn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Adding...');

        // Create condition via AJAX
        $.ajax({
            url: '{{ url("admin/k12connections/manage-email-schedules/conditions/create") }}',
            type: 'POST',
            data: {
                _token: $('meta[name="csrf-token"]').attr('content'),
                model_name: model,
                relation_name: relation,
                field_name: field,
                operator: operator,
                value: value,
                logical_operator: logical,
                description: description
            },
            success: function(response) {
                if (response.success) {
                    // Add to created conditions array
                    createdConditions.push(response.condition);

                    // Add to table
                    const tableBody = $('#createdConditionsTable tbody');
                    const row = `
                        <tr data-condition-id="${response.condition.id}">
                            <td>${modelText}</td>
                            <td>${fieldText}</td>
                            <td><code>${operator}</code></td>
                            <td>${value}</td>
                            <td>${logical}</td>
                            <td>${description || 'N/A'}</td>
                            <td>
                                <button type="button" class="btn btn-sm btn-danger remove-condition" data-id="${response.condition.id}">
                                    <i class="fa fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                    `;
                    tableBody.append(row);

                    // Show the table
                    $('#createdConditionsCard').show();

                    // Add to existing conditions dropdown
                    $('#condition_ids').append(`<option value="${response.condition.id}" selected>${response.condition.text}</option>`);
                    $('#condition_ids').trigger('change');

                    // Clear form
                    $('#new_condition_model').val('');
                    $('#new_condition_relation').val('');
                    $('#new_condition_field').empty().append('<option value="">Select Field</option>').prop('disabled', true);
                    $('#new_condition_operator').val('');
                    $('#new_condition_value').val('');
                    $('#new_condition_description').val('');

                    alertify.success(response.message);
                } else {
                    alertify.error(response.message || 'Failed to create condition');
                }
            },
            error: function(xhr) {
                let message = 'Failed to create condition';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                    const errors = Object.values(xhr.responseJSON.errors).flat();
                    message = errors.join(', ');
                }
                alertify.error(message);
            },
            complete: function() {
                btn.prop('disabled', false).html(originalText);
            }
        });
    });

    // Handle remove condition
    $(document).on('click', '.remove-condition', function() {
        const conditionId = $(this).data('id');
        const row = $(this).closest('tr');

        if (confirm('Are you sure you want to remove this condition?')) {
            // Remove from array
            createdConditions = createdConditions.filter(c => c.id != conditionId);

            // Remove from table
            row.remove();

            // Remove from dropdown
            $(`#condition_ids option[value="${conditionId}"]`).remove();
            $('#condition_ids').trigger('change');

            // Hide table if empty
            if ($('#createdConditionsTable tbody tr').length === 0) {
                $('#createdConditionsCard').hide();
            }

            alertify.success('Condition removed');
        }
    });
});
</script>
@endsection
